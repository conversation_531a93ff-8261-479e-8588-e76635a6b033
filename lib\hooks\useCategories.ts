import { useQuery } from '@tanstack/react-query';
import { categoriesApi, type Category } from '../api';

// Query keys for categories
export const categoriesKeys = {
  all: ['categories'] as const,
  lists: () => [...categoriesKeys.all, 'list'] as const,
  list: (filters: string) => [...categoriesKeys.lists(), { filters }] as const,
  details: () => [...categoriesKeys.all, 'detail'] as const,
  detail: (id: number) => [...categoriesKeys.details(), id] as const,
};

// Hook to fetch all categories
export function useCategories() {
  return useQuery({
    queryKey: categoriesKeys.lists(),
    queryFn: categoriesApi.getAll,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
  });
}

// Hook to fetch a single category by ID
export function useCategory(id: number) {
  return useQuery({
    queryKey: categoriesKeys.detail(id),
    queryFn: () => categoriesApi.getById(id),
    enabled: !!id, // Only run query if id is provided
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}
