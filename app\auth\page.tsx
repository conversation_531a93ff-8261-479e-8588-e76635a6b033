"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { User, Briefcase, Mail, Lock, Eye, EyeOff, Loader2 } from "lucide-react";
import { Footer } from "@/components/footer";
import { useAuth } from "@/lib/auth/auth-context";

export default function AuthPage() {
  const [userType, setUserType] = useState<"client" | "provider">("client");
  const [isLogin, setIsLogin] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  // Form data
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const { login, isAuthenticated, user } = useAuth();
  const router = useRouter();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      if (user.role === "provider") {
        router.push("/providers/dashboard");
      } else {
        router.push("/dashboard");
      }
    }
  }, [isAuthenticated, user, router]);

  // Show loading if redirecting
  if (isAuthenticated && user) {
    return (
      <div className="min-h-screen bg-[#F0F2F5] flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-[#1877F2] mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-[#1C1E21] mb-2">جاري التوجيه...</h2>
          <p className="text-gray-600">يرجى الانتظار</p>
        </div>
      </div>
    );
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    if (!isLogin) {
      // Handle registration (you can implement this later)
      setError("التسجيل غير متاح حالياً. يرجى تسجيل الدخول.");
      return;
    }

    if (!email || !password) {
      setError("يرجى ملء جميع الحقول المطلوبة");
      return;
    }

    setIsLoading(true);

    try {
      await login(email, password);
      // Redirect will happen automatically via the auth context
    } catch (err: any) {
      setError(err.message || "حدث خطأ في تسجيل الدخول");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#F0F2F5]">
      <div className="py-8">
        <div className="max-w-md mx-auto px-4">
          <div className="card">
            {/* Header */}
            <div className="text-center mb-6">
              <Link href="/" className="flex items-center justify-center space-x-2 space-x-reverse mb-4">
                <div className="w-10 h-10 bg-[#1877F2] rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-xl">م</span>
                </div>
                <span className="text-2xl font-bold text-[#1C1E21]">M3allam</span>
              </Link>
              <h1 className="text-2xl font-bold text-[#1C1E21] mb-2">{isLogin ? "تسجيل الدخول" : "إنشاء حساب جديد"}</h1>
              <p className="text-gray-600">{isLogin ? "مرحباً بعودتك!" : "انضم إلى منصة M3allam"}</p>
            </div>

            {/* User Type Selection */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-3">نوع الحساب</label>
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => setUserType("client")}
                  className={`p-3 rounded-lg border-2 transition-colors ${
                    userType === "client" ? "border-[#1877F2] bg-blue-50 text-[#1877F2]" : "border-gray-300 text-gray-600 hover:border-gray-400"
                  }`}
                >
                  <User className="w-6 h-6 mx-auto mb-2" />
                  <span className="text-sm font-medium">عميل</span>
                </button>
                <button
                  onClick={() => setUserType("provider")}
                  className={`p-3 rounded-lg border-2 transition-colors ${
                    userType === "provider" ? "border-[#1877F2] bg-blue-50 text-[#1877F2]" : "border-gray-300 text-gray-600 hover:border-gray-400"
                  }`}
                >
                  <Briefcase className="w-6 h-6 mx-auto mb-2" />
                  <span className="text-sm font-medium">مقدم خدمة</span>
                </button>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}

            {/* Form */}
            <form onSubmit={handleSubmit} className="space-y-4">
              {!isLogin && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل</label>
                  <input
                    type="text"
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                    placeholder="أدخل اسمك الكامل"
                  />
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                <div className="relative">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full p-3 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                    placeholder="أدخل بريدك الإلكتروني"
                    required
                  />
                  <Mail className="absolute right-3 top-3.5 w-4 h-4 text-gray-400" />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full p-3 pr-10 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                    placeholder="أدخل كلمة المرور"
                    required
                  />
                  <Lock className="absolute right-3 top-3.5 w-4 h-4 text-gray-400" />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute left-3 top-3.5 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
              </div>

              {!isLogin && userType === "provider" && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                    <input
                      type="tel"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                      placeholder="05xxxxxxxx"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">التخصص</label>
                    <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent">
                      <option>اختر تخصصك</option>
                      <option>الكهرباء</option>
                      <option>السباكة</option>
                      <option>التنظيف</option>
                      <option>التكييف</option>
                      <option>النجارة</option>
                      <option>الدهان</option>
                    </select>
                  </div>
                </>
              )}

              {isLogin && (
                <div className="flex items-center justify-between">
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded border-gray-300 text-[#1877F2] focus:ring-[#1877F2]" />
                    <span className="mr-2 text-sm text-gray-600">تذكرني</span>
                  </label>
                  <Link href="#" className="text-sm text-[#1877F2] hover:underline">
                    نسيت كلمة المرور؟
                  </Link>
                </div>
              )}

              <button
                type="submit"
                disabled={isLoading}
                className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
              >
                {isLoading && <Loader2 className="w-4 h-4 animate-spin" />}
                {isLoading ? "جاري التحقق..." : isLogin ? "تسجيل الدخول" : "إنشاء الحساب"}
              </button>
            </form>

            {/* Toggle */}
            <div className="text-center mt-6">
              <p className="text-gray-600">
                {isLogin ? "ليس لديك حساب؟" : "لديك حساب بالفعل؟"}
                <button onClick={() => setIsLogin(!isLogin)} className="text-[#1877F2] hover:underline mr-1">
                  {isLogin ? "إنشاء حساب جديد" : "تسجيل الدخول"}
                </button>
              </p>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
