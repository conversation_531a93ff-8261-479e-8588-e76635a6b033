const API_BASE_URL = 'http://localhost:7000';

export interface Category {
  id: number;
  name: string;
  description: string;
  icon: string;
  image?: string;
}

export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

// Generic API fetch function
async function apiRequest<T>(endpoint: string): Promise<T> {
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`API Error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  return data;
}

// Categories API functions
export const categoriesApi = {
  getAll: (): Promise<Category[]> => apiRequest<Category[]>('/api/categories'),
  getById: (id: number): Promise<Category> => apiRequest<Category>(`/api/categories/${id}`),
};
