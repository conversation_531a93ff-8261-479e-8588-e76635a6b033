# React Query Setup Documentation

## Overview
This document describes the React Query implementation for fetching categories from the backend API.

## Files Created/Modified

### 1. Dependencies Added
- `@tanstack/react-query` - Main React Query library
- `@tanstack/react-query-devtools` - Development tools for debugging

### 2. API Layer (`lib/api.ts`)
- Contains API configuration and functions
- Defines TypeScript interfaces for data types
- Implements generic API request function with error handling
- Exports `categoriesApi` object with methods:
  - `getAll()` - Fetch all categories
  - `getById(id)` - Fetch single category by ID

### 3. React Query Hooks (`lib/hooks/useCategories.ts`)
- `useCategories()` - Hook to fetch all categories
- `useCategory(id)` - Hook to fetch single category
- Implements proper query keys for caching
- Configures stale time and garbage collection time

### 4. Query Provider (`lib/providers/query-provider.tsx`)
- Sets up QueryClient with default options
- Includes React Query DevTools for development
- Configures retry logic and stale time

### 5. App Layout (`app/layout.tsx`)
- Wraps the app with QueryProvider
- Enables React Query throughout the application

### 6. Categories Page (`app/categories/page.tsx`)
- Converted to client component (`'use client'`)
- Uses `useCategories()` hook instead of static data
- Implements loading states with spinner
- Implements error states with retry functionality
- Handles empty state when no categories are available

## API Configuration

### Backend URL
```typescript
const API_BASE_URL = 'http://localhost:7000';
```

### API Endpoint
- Categories: `GET /api/categories`

## Features Implemented

### 1. Loading States
- Shows spinner and loading message while fetching data
- Maintains page layout during loading

### 2. Error Handling
- Displays user-friendly error messages in Arabic
- Provides retry button to reload the page
- Automatic retry logic (up to 3 times for non-4xx errors)

### 3. Empty States
- Shows message when no categories are available

### 4. Caching
- 5-minute stale time for categories
- 10-minute garbage collection time
- Automatic background refetching

### 5. Development Tools
- React Query DevTools available in development mode
- Query inspection and debugging capabilities

## Usage

### Fetching All Categories
```typescript
import { useCategories } from '@/lib/hooks/useCategories';

function MyComponent() {
  const { data: categories, isLoading, error } = useCategories();
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error occurred</div>;
  
  return (
    <div>
      {categories?.map(category => (
        <div key={category.id}>{category.name}</div>
      ))}
    </div>
  );
}
```

### Fetching Single Category
```typescript
import { useCategory } from '@/lib/hooks/useCategories';

function CategoryDetail({ id }: { id: number }) {
  const { data: category, isLoading, error } = useCategory(id);
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error occurred</div>;
  
  return <div>{category?.name}</div>;
}
```

## Next Steps

1. **Test the implementation** by starting the backend server on `http://localhost:7000`
2. **Add similar hooks** for other entities (services, providers, etc.)
3. **Implement mutations** for creating/updating/deleting data
4. **Add optimistic updates** for better user experience
5. **Consider adding infinite queries** for pagination if needed

## Backend Requirements

The backend should return categories in the following format:

```typescript
// GET /api/categories
[
  {
    id: number;
    name: string;
    description: string;
    icon: string;
    image?: string;
  }
]
```

Make sure your backend API is running on `http://localhost:7000` and returns data in this format.
