// Environment configuration
export const config = {
  // API Configuration
  apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:7000',
  
  // Authentication Configuration
  authTokenKey: process.env.NEXT_PUBLIC_AUTH_TOKEN_KEY || 'auth_token',
  refreshTokenKey: process.env.NEXT_PUBLIC_AUTH_REFRESH_TOKEN_KEY || 'refresh_token',
  
  // App Configuration
  appName: process.env.NEXT_PUBLIC_APP_NAME || 'M3allam',
  appDescription: process.env.NEXT_PUBLIC_APP_DESCRIPTION || 'منصة الخدمات المنزلية',
  
  // Environment
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
  
  // File Upload Configuration
  maxFileSize: parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '5242880'), // 5MB
  allowedFileTypes: process.env.NEXT_PUBLIC_ALLOWED_FILE_TYPES?.split(',') || [
    'image/jpeg',
    'image/png',
    'image/webp'
  ],
  
  // Pagination Configuration
  defaultPageSize: parseInt(process.env.NEXT_PUBLIC_DEFAULT_PAGE_SIZE || '10'),
  maxPageSize: parseInt(process.env.NEXT_PUBLIC_MAX_PAGE_SIZE || '100'),
  
  // Cache Configuration
  cacheDuration: parseInt(process.env.NEXT_PUBLIC_CACHE_DURATION || '300000'), // 5 minutes
  
  // Feature Flags
  enableRegistration: process.env.NEXT_PUBLIC_ENABLE_REGISTRATION === 'true',
  enableSocialLogin: process.env.NEXT_PUBLIC_ENABLE_SOCIAL_LOGIN === 'true',
  enableEmailVerification: process.env.NEXT_PUBLIC_ENABLE_EMAIL_VERIFICATION === 'true',
  
  // Contact Information
  supportEmail: process.env.NEXT_PUBLIC_SUPPORT_EMAIL || '<EMAIL>',
  supportPhone: process.env.NEXT_PUBLIC_SUPPORT_PHONE || '+966501234567',
  
  // Social Media Links
  socialMedia: {
    facebook: process.env.NEXT_PUBLIC_FACEBOOK_URL || '',
    twitter: process.env.NEXT_PUBLIC_TWITTER_URL || '',
    instagram: process.env.NEXT_PUBLIC_INSTAGRAM_URL || '',
  },
  
  // Analytics
  googleAnalyticsId: process.env.NEXT_PUBLIC_GA_TRACKING_ID || '',
  
  // Maps
  googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '',
  
  // Payment
  payment: {
    stripePublishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '',
    paypalClientId: process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID || '',
  },
  
  // API Endpoints
  endpoints: {
    auth: {
      login: '/api/auth/login',
      me: '/api/auth/me',
      logout: '/api/auth/logout',
      register: '/api/auth/register',
    },
    categories: '/api/categories',
    services: '/api/services',
    providers: '/api/providers',
    bookings: '/api/bookings',
    reviews: '/api/reviews',
  },
} as const;

// Type-safe environment variable getter
export function getEnvVar(key: string, defaultValue?: string): string {
  const value = process.env[key];
  if (!value && !defaultValue) {
    throw new Error(`Environment variable ${key} is required but not set`);
  }
  return value || defaultValue || '';
}

// Validate required environment variables
export function validateConfig() {
  const requiredVars = [
    'NEXT_PUBLIC_API_BASE_URL',
  ];
  
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    console.warn(`Missing environment variables: ${missing.join(', ')}`);
    console.warn('Using default values. Please check your .env file.');
  }
}

// Initialize configuration validation
if (typeof window === 'undefined') {
  // Only validate on server side
  validateConfig();
}
