import { useQuery } from '@tanstack/react-query';
import { providersApi, type Provider } from '../api';

// Query keys for providers
export const providersKeys = {
  all: ['providers'] as const,
  lists: () => [...providersKeys.all, 'list'] as const,
  list: (filters: string) => [...providersKeys.lists(), { filters }] as const,
  details: () => [...providersKeys.all, 'detail'] as const,
  detail: (id: number) => [...providersKeys.details(), id] as const,
  byCategory: (categoryId: number) => [...providersKeys.all, 'category', categoryId] as const,
};

// Hook to fetch all providers
export function useProviders() {
  return useQuery({
    queryKey: providersKeys.lists(),
    queryFn: providersApi.getAll,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook to fetch a single provider by ID
export function useProvider(id: number) {
  return useQuery({
    queryKey: providersKeys.detail(id),
    queryFn: () => providersApi.getById(id),
    enabled: !!id, // Only run query if id is provided
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook to fetch providers by category ID
export function useProvidersByCategory(categoryId: number) {
  return useQuery({
    queryKey: providersKeys.byCategory(categoryId),
    queryFn: () => providersApi.getByCategory(categoryId),
    enabled: !!categoryId, // Only run query if categoryId is provided
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}
