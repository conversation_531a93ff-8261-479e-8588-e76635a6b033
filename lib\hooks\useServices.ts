import { useQuery } from '@tanstack/react-query';
import { servicesApi, type Service } from '../api';

// Query keys for services
export const servicesKeys = {
  all: ['services'] as const,
  lists: () => [...servicesKeys.all, 'list'] as const,
  list: (filters: string) => [...servicesKeys.lists(), { filters }] as const,
  details: () => [...servicesKeys.all, 'detail'] as const,
  detail: (id: number) => [...servicesKeys.details(), id] as const,
  byCategory: (categoryId: number) => [...servicesKeys.all, 'category', categoryId] as const,
};

// Hook to fetch all services
export function useServices() {
  return useQuery({
    queryKey: servicesKeys.lists(),
    queryFn: servicesApi.getAll,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook to fetch a single service by ID
export function useService(id: number) {
  return useQuery({
    queryKey: servicesKeys.detail(id),
    queryFn: () => servicesApi.getById(id),
    enabled: !!id, // Only run query if id is provided
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook to fetch services by category ID
export function useServicesByCategory(categoryId: number) {
  return useQuery({
    queryKey: servicesKeys.byCategory(categoryId),
    queryFn: () => servicesApi.getByCategory(categoryId),
    enabled: !!categoryId, // Only run query if categoryId is provided
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}
