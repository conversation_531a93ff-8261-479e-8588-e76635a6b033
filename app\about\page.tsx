import Link from "next/link"
import Image from "next/image"
import { ArrowR<PERSON>, Users, Award } from "lucide-react"
import { Footer } from "@/components/footer"

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-[#F0F2F5]">
      {/* Hero Section */}
      <section className="bg-[#1877F2] text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">من نحن</h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              تعرف على قصة M3allam ورؤيتنا لتغيير طريقة الحصول على الخدمات المنزلية
            </p>
          </div>
        </div>
      </section>

      {/* Breadcrumb */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-2 text-sm">
            <Link href="/" className="text-[#1877F2] hover:underline">
              الرئيسية
            </Link>
            <ArrowRight className="w-4 h-4 text-gray-400" />
            <span className="text-gray-600">من نحن</span>
          </div>
        </div>
      </div>

      {/* Our Story */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-[#1C1E21] mb-6">قصتنا</h2>
              <p className="text-gray-600 mb-4">
                بدأت M3allam في عام 2020 كفكرة بسيطة لحل مشكلة يواجهها الكثيرون: صعوبة العثور على مقدمي خدمات منزلية
                موثوقين وبأسعار معقولة.
              </p>
              <p className="text-gray-600 mb-4">
                أسس الشركة فريق من المهندسين والمطورين الذين يؤمنون بأن التكنولوجيا يمكن أن تساعد في تبسيط الحياة
                اليومية وتوفير الوقت والجهد.
              </p>
              <p className="text-gray-600">
                اليوم، أصبحت M3allam منصة رائدة تربط بين آلاف العملاء ومقدمي الخدمات المحترفين في جميع أنحاء المملكة
                العربية السعودية.
              </p>
            </div>
            <div className="order-first md:order-last">
              <Image
                src="/placeholder.svg?height=400&width=500"
                alt="قصة M3allam"
                width={500}
                height={400}
                className="rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Our Mission */}
      <section className="py-16 bg-[#F0F2F5]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-[#1C1E21] mb-4">مهمتنا ورؤيتنا</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              نسعى لتغيير طريقة حصول الناس على الخدمات المنزلية وتحسين حياتهم اليومية
            </p>
          </div>
          <div className="grid md:grid-cols-2 gap-8">
            <div className="card">
              <div className="w-16 h-16 bg-[#1877F2] rounded-full flex items-center justify-center mb-6">
                <Award className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-[#1C1E21] mb-3">مهمتنا</h3>
              <p className="text-gray-600">
                توفير منصة سهلة الاستخدام تربط بين العملاء ومقدمي الخدمات المنزلية المحترفين، مع ضمان جودة الخدمة
                والشفافية في الأسعار والمواعيد.
              </p>
            </div>
            <div className="card">
              <div className="w-16 h-16 bg-[#1877F2] rounded-full flex items-center justify-center mb-6">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-[#1C1E21] mb-3">رؤيتنا</h3>
              <p className="text-gray-600">
                أن نصبح المنصة الرائدة في مجال الخدمات المنزلية في الشرق الأوسط، ونساهم في خلق فرص عمل للمهنيين وتحسين
                جودة الخدمات المقدمة للعملاء.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-[#1C1E21] mb-4">قيمنا</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">المبادئ التي توجه عملنا وتحدد هويتنا</p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="card text-center">
              <div className="text-4xl mb-4">🤝</div>
              <h3 className="text-xl font-bold mb-3">الثقة</h3>
              <p className="text-gray-600">نبني علاقات قائمة على الثقة مع عملائنا ومقدمي الخدمات</p>
            </div>
            <div className="card text-center">
              <div className="text-4xl mb-4">⚡</div>
              <h3 className="text-xl font-bold mb-3">الكفاءة</h3>
              <p className="text-gray-600">نسعى دائماً لتحسين خدماتنا وتوفير تجربة سلسة وفعالة</p>
            </div>
            <div className="card text-center">
              <div className="text-4xl mb-4">💎</div>
              <h3 className="text-xl font-bold mb-3">الجودة</h3>
              <p className="text-gray-600">نضمن أعلى معايير الجودة في جميع الخدمات المقدمة عبر منصتنا</p>
            </div>
          </div>
        </div>
      </section>

      {/* Team */}
      <section className="py-16 bg-[#F0F2F5]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-[#1C1E21] mb-4">فريقنا</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">تعرف على الأشخاص الذين يقفون وراء M3allam</p>
          </div>
          <div className="grid md:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="card text-center">
                <img
                  src={`/placeholder.svg?height=150&width=150`}
                  alt={`عضو الفريق ${i}`}
                  className="w-24 h-24 rounded-full object-cover mx-auto mb-4"
                />
                <h3 className="text-lg font-bold text-[#1C1E21] mb-1">أحمد محمد</h3>
                <p className="text-[#1877F2] mb-2">المؤسس والرئيس التنفيذي</p>
                <p className="text-sm text-gray-600">خبرة أكثر من 10 سنوات في مجال التقنية وريادة الأعمال</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-16 bg-[#1877F2] text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">انضم إلى مجتمع M3allam</h2>
          <p className="text-xl mb-8 text-blue-100 max-w-3xl mx-auto">
            سواء كنت تبحث عن خدمة منزلية أو كنت مقدم خدمة محترف، انضم إلينا اليوم واستفد من منصتنا
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/categories" className="btn-primary bg-white text-[#1877F2] hover:bg-gray-100">
              استكشف الخدمات
            </Link>
            <Link
              href="/auth"
              className="btn-secondary bg-transparent border-2 border-white text-white hover:bg-white hover:text-[#1877F2]"
            >
              انضم كمقدم خدمة
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
