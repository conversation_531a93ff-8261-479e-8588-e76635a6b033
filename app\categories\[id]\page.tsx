"use client";

import Link from "next/link";
import { ArrowR<PERSON>, Users, Star, Filter, Search, MapPin, Clock, Loader2 } from "lucide-react";
import { Footer } from "@/components/footer";
import { useCategory } from "@/lib/hooks/useCategories";
import { useServicesByCategory } from "@/lib/hooks/useServices";
import { useProvidersByCategory } from "@/lib/hooks/useProviders";

export default function CategoryServicesPage({ params }: { params: { id: string } }) {
  const categoryId = Number.parseInt(params.id);

  // Fetch data using React Query
  const { data: category, isLoading: categoryLoading, error: categoryError } = useCategory(categoryId);
  const { data: categoryServices, isLoading: servicesLoading, error: servicesError } = useServicesByCategory(categoryId);
  const { data: categoryProviders, isLoading: providersLoading, error: providersError } = useProvidersByCategory(categoryId);

  // Loading state - show loading if any of the main data is loading
  if (categoryLoading || servicesLoading || providersLoading) {
    return (
      <div className="min-h-screen bg-[#F0F2F5] flex items-center justify-center">
        <div className="card text-center max-w-md mx-auto">
          <Loader2 className="h-8 w-8 animate-spin text-[#1877F2] mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-[#1C1E21] mb-3">جاري تحميل الفئة...</h2>
          <p className="text-gray-600">يرجى الانتظار</p>
        </div>
      </div>
    );
  }

  // Error state - show error if any critical data failed to load
  if (categoryError || servicesError || providersError) {
    return (
      <div className="min-h-screen bg-[#F0F2F5] flex items-center justify-center">
        <div className="card text-center max-w-md mx-auto">
          <div className="text-4xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold text-[#1C1E21] mb-3">حدث خطأ في تحميل البيانات</h2>
          <p className="text-gray-600 mb-6">يرجى المحاولة مرة أخرى</p>
          <button onClick={() => window.location.reload()} className="btn-primary">
            إعادة المحاولة
          </button>
        </div>
      </div>
    );
  }

  if (!category) {
    return (
      <div className="min-h-screen bg-[#F0F2F5] flex items-center justify-center">
        <div className="card text-center max-w-md mx-auto">
          <div className="text-4xl mb-4">❌</div>
          <h2 className="text-2xl font-bold text-[#1C1E21] mb-3">الفئة غير موجودة</h2>
          <p className="text-gray-600 mb-6">عذراً، لم يتم العثور على الفئة المطلوبة</p>
          <Link href="/categories" className="btn-primary">
            العودة إلى الفئات
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#F0F2F5]">
      <div className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Breadcrumb */}
          <div className="flex items-center gap-2 mb-6 text-sm">
            <Link href="/" className="text-[#1877F2] hover:underline">
              الرئيسية
            </Link>
            <ArrowRight className="w-4 h-4 text-gray-400" />
            <Link href="/categories" className="text-[#1877F2] hover:underline">
              الفئات
            </Link>
            <ArrowRight className="w-4 h-4 text-gray-400" />
            <span className="text-gray-600">{category.name}</span>
          </div>

          {/* Category Header */}
          <div className="card mb-8">
            <div className="flex flex-col md:flex-row items-start gap-6">
              <div className="flex items-center gap-4 flex-1">
                <div className="text-5xl">{category.icon}</div>
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-[#1C1E21] mb-2">{category.name}</h1>
                  <p className="text-lg text-gray-600 mb-4">{category.description}</p>
                  <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <Users className="w-4 h-4" />
                      <span>{categoryProviders?.length || 0} مقدم خدمة متاح</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span>تقييم عالي</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      <span>خدمة سريعة</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row gap-3">
                <Link href="/auth" className="btn-primary">
                  احجز خدمة الآن
                </Link>
                <Link href="/auth" className="btn-secondary">
                  انضم كمقدم خدمة
                </Link>
              </div>
            </div>
          </div>

          {/* Search and Filter Bar */}
          <div className="card mb-8">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute right-3 top-3 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="ابحث عن خدمة أو مقدم خدمة..."
                  className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                />
              </div>
              <div className="flex gap-3">
                <select className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent">
                  <option>ترتيب حسب</option>
                  <option>الأعلى تقييماً</option>
                  <option>الأقل سعراً</option>
                  <option>الأكثر خبرة</option>
                </select>
                <button className="flex items-center gap-2 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                  <Filter className="w-4 h-4" />
                  <span>فلترة</span>
                </button>
              </div>
            </div>
          </div>

          <div className="grid lg:grid-cols-4 gap-8">
            {/* Sidebar Filters */}
            <div className="lg:col-span-1">
              <div className="card mb-6">
                <h3 className="text-lg font-bold text-[#1C1E21] mb-4">فلترة النتائج</h3>

                {/* Price Range */}
                <div className="mb-6">
                  <h4 className="font-medium text-[#1C1E21] mb-3">نطاق السعر</h4>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-[#1877F2] focus:ring-[#1877F2]" />
                      <span className="mr-2 text-sm">أقل من 100 ريال</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-[#1877F2] focus:ring-[#1877F2]" />
                      <span className="mr-2 text-sm">100 - 200 ريال</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-[#1877F2] focus:ring-[#1877F2]" />
                      <span className="mr-2 text-sm">200 - 300 ريال</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-[#1877F2] focus:ring-[#1877F2]" />
                      <span className="mr-2 text-sm">أكثر من 300 ريال</span>
                    </label>
                  </div>
                </div>

                {/* Rating */}
                <div className="mb-6">
                  <h4 className="font-medium text-[#1C1E21] mb-3">التقييم</h4>
                  <div className="space-y-2">
                    {[5, 4, 3].map((rating) => (
                      <label key={rating} className="flex items-center">
                        <input type="checkbox" className="rounded border-gray-300 text-[#1877F2] focus:ring-[#1877F2]" />
                        <div className="flex items-center mr-2">
                          {Array.from({ length: rating }, (_, i) => (
                            <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                          ))}
                          <span className="text-sm mr-1">فأكثر</span>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Location */}
                <div className="mb-6">
                  <h4 className="font-medium text-[#1C1E21] mb-3">المنطقة</h4>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-[#1877F2] focus:ring-[#1877F2]" />
                      <span className="mr-2 text-sm">الرياض</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-[#1877F2] focus:ring-[#1877F2]" />
                      <span className="mr-2 text-sm">جدة</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-[#1877F2] focus:ring-[#1877F2]" />
                      <span className="mr-2 text-sm">الدمام</span>
                    </label>
                  </div>
                </div>

                {/* Availability */}
                <div>
                  <h4 className="font-medium text-[#1C1E21] mb-3">التوفر</h4>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-[#1877F2] focus:ring-[#1877F2]" />
                      <span className="mr-2 text-sm">متاح اليوم</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-[#1877F2] focus:ring-[#1877F2]" />
                      <span className="mr-2 text-sm">متاح غداً</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-[#1877F2] focus:ring-[#1877F2]" />
                      <span className="mr-2 text-sm">متاح هذا الأسبوع</span>
                    </label>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="card">
                <h3 className="text-lg font-bold text-[#1C1E21] mb-4">إجراءات سريعة</h3>
                <div className="space-y-3">
                  <Link href="/auth" className="block w-full btn-primary text-center">
                    طلب عرض سعر
                  </Link>
                  <Link href="/contact" className="block w-full btn-secondary text-center">
                    تحدث مع خبير
                  </Link>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Services Section */}
              <div className="mb-8">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold text-[#1C1E21]">الخدمات المتاحة</h2>
                  <span className="text-sm text-gray-600">{categoryServices?.length || 0} خدمة</span>
                </div>

                {categoryServices && categoryServices.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {categoryServices.map((service) => (
                      <div key={service.id} className="card hover:shadow-lg transition-all duration-300 group">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <h3 className="text-xl font-bold text-[#1C1E21] mb-2 group-hover:text-[#1877F2] transition-colors">{service.name}</h3>
                            <p className="text-gray-600 mb-3">{service.description}</p>
                            <div className="flex items-center gap-4 text-sm text-gray-500">
                              <span>⏱️ 1-2 ساعة</span>
                              <span>✅ ضمان الجودة</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <span className="text-2xl font-bold text-[#1877F2]">{service.price}</span>
                            <span className="text-sm text-gray-500 mr-1">ريال</span>
                          </div>
                          <Link href={`/providers?service=${service.id}`} className="btn-primary text-sm group-hover:bg-[#166FE5] transition-colors">
                            عرض المقدمين
                          </Link>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="card text-center py-12">
                    <div className="text-6xl mb-4">🔍</div>
                    <h3 className="text-2xl font-bold text-[#1C1E21] mb-3">لا توجد خدمات متاحة</h3>
                    <p className="text-gray-600 mb-6 max-w-md mx-auto">
                      لم يتم العثور على خدمات في هذه الفئة حالياً. يمكنك تصفح فئات أخرى أو طلب خدمة مخصصة.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3 justify-center">
                      <Link href="/categories" className="btn-primary">
                        تصفح فئات أخرى
                      </Link>
                      <Link href="/contact" className="btn-secondary">
                        طلب خدمة مخصصة
                      </Link>
                    </div>
                  </div>
                )}
              </div>

              {/* Top Providers Section */}
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold text-[#1C1E21]">أفضل مقدمي الخدمات</h2>
                  <span className="text-sm text-gray-600">{categoryProviders?.length || 0} مقدم خدمة</span>
                </div>

                {categoryProviders && categoryProviders.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {categoryProviders.map((provider) => (
                      <Link key={provider.id} href={`/providers/${provider.id}`} className="card hover:shadow-lg transition-all duration-300 group">
                        <div className="flex items-start gap-4 mb-4">
                          <img
                            src={provider.image || "/placeholder.svg"}
                            alt={provider.name}
                            className="w-16 h-16 rounded-full object-cover border-2 border-gray-200 group-hover:border-[#1877F2] transition-colors"
                          />
                          <div className="flex-1">
                            <h3 className="font-bold text-[#1C1E21] mb-1 group-hover:text-[#1877F2] transition-colors">{provider.name}</h3>
                            <div className="flex items-center gap-2 mb-2">
                              <div className="flex items-center gap-1">
                                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                                <span className="text-sm font-medium">{provider.rating}</span>
                              </div>
                              <span className="text-sm text-gray-500">({provider.completedJobs} خدمة)</span>
                            </div>
                            <div className="flex items-center gap-1 text-sm text-gray-600">
                              <MapPin className="w-3 h-3" />
                              <span>{provider.location}</span>
                            </div>
                          </div>
                          <div className="text-left">
                            <div className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium mb-2">متاح الآن</div>
                          </div>
                        </div>

                        <p className="text-gray-600 text-sm mb-4 line-clamp-2">{provider.bio}</p>

                        <div className="flex flex-wrap gap-1 mb-4">
                          {provider.services.slice(0, 3).map((service, index) => (
                            <span key={index} className="bg-blue-50 text-[#1877F2] px-2 py-1 rounded text-xs">
                              {service}
                            </span>
                          ))}
                          {provider.services.length > 3 && <span className="text-xs text-gray-500">+{provider.services.length - 3} أخرى</span>}
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="text-sm text-gray-500">
                            <Clock className="w-4 h-4 inline ml-1" />
                            {provider.availability}
                          </div>
                          <span className="text-[#1877F2] font-medium text-sm group-hover:underline">عرض الملف الكامل</span>
                        </div>
                      </Link>
                    ))}
                  </div>
                ) : (
                  <div className="card text-center py-12">
                    <div className="text-6xl mb-4">👨‍🔧</div>
                    <h3 className="text-2xl font-bold text-[#1C1E21] mb-3">لا يوجد مقدمو خدمات</h3>
                    <p className="text-gray-600 mb-6 max-w-md mx-auto">
                      لم يتم العثور على مقدمي خدمات في هذه الفئة حالياً. يمكنك تصفح فئات أخرى أو الانضمام كمقدم خدمة.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3 justify-center">
                      <Link href="/categories" className="btn-primary">
                        تصفح فئات أخرى
                      </Link>
                      <Link href="/auth" className="btn-secondary">
                        انضم كمقدم خدمة
                      </Link>
                    </div>
                  </div>
                )}
              </div>

              {/* Call to Action */}
              {((categoryServices?.length || 0) > 0 || (categoryProviders?.length || 0) > 0) && (
                <div className="card mt-8 bg-gradient-to-r from-[#1877F2] to-[#166FE5] text-white text-center">
                  <h3 className="text-2xl font-bold mb-3">هل تحتاج مساعدة في اختيار الخدمة المناسبة؟</h3>
                  <p className="text-blue-100 mb-6">فريقنا من الخبراء جاهز لمساعدتك في العثور على أفضل مقدم خدمة لاحتياجاتك</p>
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <Link href="/contact" className="btn-primary bg-white text-[#1877F2] hover:bg-gray-100">
                      تحدث مع خبير
                    </Link>
                    <Link href="/auth" className="btn-secondary bg-transparent border-2 border-white text-white hover:bg-white hover:text-[#1877F2]">
                      احجز استشارة مجانية
                    </Link>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
