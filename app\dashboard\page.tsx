"use client";

import { useState } from "react";
import { Calendar, Clock, Star, User, Setting<PERSON>, <PERSON>, History } from "lucide-react";
import { bookings } from "@/lib/data";
import { Footer } from "@/components/footer";
import { ProtectedRoute } from "@/components/auth/protected-route";

export default function DashboardPage() {
  const [activeTab, setActiveTab] = useState("overview");

  const tabs = [
    { id: "overview", label: "نظرة عامة", icon: User },
    { id: "bookings", label: "حجوزاتي", icon: Calendar },
    { id: "history", label: "السجل", icon: History },
    { id: "profile", label: "الملف الشخصي", icon: Settings },
  ];

  return (
    <ProtectedRoute requiredRole="client">
      <div className="min-h-screen bg-[#F0F2F5]">
        <div className="py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-[#1C1E21] mb-2">لوحة التحكم</h1>
              <p className="text-gray-600">مرحباً بك، أحمد محمد</p>
            </div>

            <div className="grid lg:grid-cols-4 gap-8">
              {/* Sidebar */}
              <div className="lg:col-span-1">
                <div className="card">
                  <div className="flex items-center gap-3 mb-6 pb-6 border-b border-gray-200">
                    <img src="/placeholder.svg?height=60&width=60" alt="صورة المستخدم" className="w-15 h-15 rounded-full object-cover" />
                    <div>
                      <h3 className="font-bold text-[#1C1E21]">أحمد محمد</h3>
                      <p className="text-sm text-gray-600">عميل</p>
                      <div className="flex items-center gap-1 mt-1">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="text-sm text-gray-600">4.8</span>
                      </div>
                    </div>
                  </div>

                  <nav className="space-y-2">
                    {tabs.map((tab) => {
                      const Icon = tab.icon;
                      return (
                        <button
                          key={tab.id}
                          onClick={() => setActiveTab(tab.id)}
                          className={`w-full flex items-center gap-3 p-3 rounded-lg text-right transition-colors ${
                            activeTab === tab.id ? "bg-[#1877F2] text-white" : "text-gray-700 hover:bg-gray-100"
                          }`}
                        >
                          <Icon className="w-5 h-5" />
                          <span>{tab.label}</span>
                        </button>
                      );
                    })}
                  </nav>
                </div>
              </div>

              {/* Main Content */}
              <div className="lg:col-span-3">
                {activeTab === "overview" && (
                  <div className="space-y-6">
                    {/* Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="card text-center">
                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                          <Calendar className="w-6 h-6 text-[#1877F2]" />
                        </div>
                        <h3 className="text-2xl font-bold text-[#1C1E21] mb-1">12</h3>
                        <p className="text-gray-600">إجمالي الحجوزات</p>
                      </div>
                      <div className="card text-center">
                        <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                          <Clock className="w-6 h-6 text-green-600" />
                        </div>
                        <h3 className="text-2xl font-bold text-[#1C1E21] mb-1">2</h3>
                        <p className="text-gray-600">حجوزات قادمة</p>
                      </div>
                      <div className="card text-center">
                        <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                          <Star className="w-6 h-6 text-yellow-600" />
                        </div>
                        <h3 className="text-2xl font-bold text-[#1C1E21] mb-1">4.8</h3>
                        <p className="text-gray-600">متوسط التقييم</p>
                      </div>
                    </div>

                    {/* Recent Bookings */}
                    <div className="card">
                      <h3 className="text-lg font-bold text-[#1C1E21] mb-4">الحجوزات الأخيرة</h3>
                      <div className="space-y-4">
                        {bookings.slice(0, 3).map((booking) => (
                          <div key={booking.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                              <h4 className="font-medium text-[#1C1E21]">{booking.service}</h4>
                              <p className="text-sm text-gray-600">{booking.provider}</p>
                              <p className="text-sm text-gray-500">{booking.date}</p>
                            </div>
                            <div className="text-left">
                              <span
                                className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                                  booking.status === "مكتملة" ? "bg-green-100 text-green-800" : "bg-blue-100 text-blue-800"
                                }`}
                              >
                                {booking.status}
                              </span>
                              <p className="text-sm font-bold text-[#1C1E21] mt-1">{booking.price}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Notifications */}
                    <div className="card">
                      <h3 className="text-lg font-bold text-[#1C1E21] mb-4">الإشعارات</h3>
                      <div className="space-y-3">
                        <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                          <Bell className="w-5 h-5 text-[#1877F2] mt-0.5" />
                          <div>
                            <p className="text-sm font-medium text-[#1C1E21]">تأكيد الحجز</p>
                            <p className="text-sm text-gray-600">تم تأكيد حجزك مع علي السباك لتاريخ 25 يناير</p>
                            <p className="text-xs text-gray-500 mt-1">منذ ساعتين</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                          <Bell className="w-5 h-5 text-green-600 mt-0.5" />
                          <div>
                            <p className="text-sm font-medium text-[#1C1E21]">اكتمال الخدمة</p>
                            <p className="text-sm text-gray-600">تم إنجاز خدمة تركيب الأسلاك بنجاح</p>
                            <p className="text-xs text-gray-500 mt-1">أمس</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === "bookings" && (
                  <div className="card">
                    <h3 className="text-lg font-bold text-[#1C1E21] mb-4">جميع الحجوزات</h3>
                    <div className="space-y-4">
                      {bookings.map((booking) => (
                        <div key={booking.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="font-bold text-[#1C1E21]">{booking.service}</h4>
                            <span
                              className={`px-3 py-1 rounded-full text-sm font-medium ${
                                booking.status === "مكتملة" ? "bg-green-100 text-green-800" : "bg-blue-100 text-blue-800"
                              }`}
                            >
                              {booking.status}
                            </span>
                          </div>
                          <div className="grid md:grid-cols-3 gap-4 text-sm">
                            <div>
                              <p className="text-gray-600">مقدم الخدمة</p>
                              <p className="font-medium">{booking.provider}</p>
                            </div>
                            <div>
                              <p className="text-gray-600">التاريخ</p>
                              <p className="font-medium">{booking.date}</p>
                            </div>
                            <div>
                              <p className="text-gray-600">السعر</p>
                              <p className="font-medium">{booking.price}</p>
                            </div>
                          </div>
                          {booking.status === "مكتملة" && (
                            <div className="mt-3 pt-3 border-t border-gray-200">
                              <button className="text-[#1877F2] text-sm hover:underline">إضافة تقييم</button>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {activeTab === "history" && (
                  <div className="card">
                    <h3 className="text-lg font-bold text-[#1C1E21] mb-4">سجل النشاطات</h3>
                    <div className="space-y-4">
                      <div className="flex items-start gap-3 p-3 border-r-4 border-green-500 bg-green-50">
                        <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                        <div>
                          <p className="font-medium text-[#1C1E21]">تم إكمال خدمة تركيب الأسلاك</p>
                          <p className="text-sm text-gray-600">مع سعيد الكهربائي - 20 يناير 2024</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3 p-3 border-r-4 border-blue-500 bg-blue-50">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                        <div>
                          <p className="font-medium text-[#1C1E21]">تم حجز خدمة تصليح سخان</p>
                          <p className="text-sm text-gray-600">مع علي السباك - 15 يناير 2024</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3 p-3 border-r-4 border-gray-500 bg-gray-50">
                        <div className="w-2 h-2 bg-gray-500 rounded-full mt-2"></div>
                        <div>
                          <p className="font-medium text-[#1C1E21]">تم إنشاء الحساب</p>
                          <p className="text-sm text-gray-600">10 يناير 2024</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === "profile" && (
                  <div className="space-y-6">
                    <div className="card">
                      <h3 className="text-lg font-bold text-[#1C1E21] mb-4">المعلومات الشخصية</h3>
                      <form className="space-y-4">
                        <div className="grid md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">الاسم الأول</label>
                            <input
                              type="text"
                              defaultValue="أحمد"
                              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">الاسم الأخير</label>
                            <input
                              type="text"
                              defaultValue="محمد"
                              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                            />
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                          <input
                            type="email"
                            defaultValue="<EMAIL>"
                            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                          <input
                            type="tel"
                            defaultValue="0501234567"
                            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                          <textarea
                            rows={3}
                            defaultValue="الرياض، حي النرجس، شارع الأمير محمد بن عبدالعزيز"
                            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                          />
                        </div>
                        <button type="submit" className="btn-primary">
                          حفظ التغييرات
                        </button>
                      </form>
                    </div>

                    <div className="card">
                      <h3 className="text-lg font-bold text-[#1C1E21] mb-4">تغيير كلمة المرور</h3>
                      <form className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">كلمة المرور الحالية</label>
                          <input
                            type="password"
                            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">كلمة المرور الجديدة</label>
                          <input
                            type="password"
                            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">تأكيد كلمة المرور الجديدة</label>
                          <input
                            type="password"
                            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                          />
                        </div>
                        <button type="submit" className="btn-primary">
                          تحديث كلمة المرور
                        </button>
                      </form>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </ProtectedRoute>
  );
}
