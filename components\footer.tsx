import Link from "next/link"

export function Footer() {
  return (
    <footer className="bg-[#1C1E21] text-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid md:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center space-x-2 space-x-reverse mb-4">
              <div className="w-8 h-8 bg-[#1877F2] rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">م</span>
              </div>
              <span className="text-xl font-bold">M3allam</span>
            </div>
            <p className="text-gray-400">منصتك الموثوقة للحصول على أفضل الخدمات المنزلية</p>
          </div>
          <div>
            <h3 className="font-bold mb-4">الخدمات</h3>
            <ul className="space-y-2 text-gray-400">
              <li>
                <Link href="/categories/1" className="hover:text-white transition-colors">
                  الكهرباء
                </Link>
              </li>
              <li>
                <Link href="/categories/2" className="hover:text-white transition-colors">
                  السباكة
                </Link>
              </li>
              <li>
                <Link href="/categories/3" className="hover:text-white transition-colors">
                  التنظيف
                </Link>
              </li>
              <li>
                <Link href="/categories/4" className="hover:text-white transition-colors">
                  التكييف
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="font-bold mb-4">الشركة</h3>
            <ul className="space-y-2 text-gray-400">
              <li>
                <Link href="/about" className="hover:text-white transition-colors">
                  من نحن
                </Link>
              </li>
              <li>
                <Link href="/contact" className="hover:text-white transition-colors">
                  اتصل بنا
                </Link>
              </li>
              <li>
                <Link href="/terms" className="hover:text-white transition-colors">
                  الشروط والأحكام
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="hover:text-white transition-colors">
                  سياسة الخصوصية
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="font-bold mb-4">تواصل معنا</h3>
            <ul className="space-y-2 text-gray-400">
              <li>البريد الإلكتروني: <EMAIL></li>
              <li>الهاتف: 920000000</li>
              <li>العنوان: الرياض، المملكة العربية السعودية</li>
            </ul>
          </div>
        </div>
        <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; {new Date().getFullYear()} M3allam. جميع الحقوق محفوظة.</p>
        </div>
      </div>
    </footer>
  )
}
