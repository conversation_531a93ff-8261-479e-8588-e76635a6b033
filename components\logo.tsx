import Image from "next/image";
import Link from "next/link";

interface LogoProps {
  className?: string;
  width?: number;
  height?: number;
  showText?: boolean;
}

export function Logo({ className = "", width = 120, height = 40, showText = true }: LogoProps) {
  return (
    <Link href="/" className={`flex items-center gap-2 ${className}`}>
      <Image src="/images/m3allam_logo.png" alt="معلم - منصة الخدمات المنزلية" width={width} height={height} className="object-contain" priority />
      {showText && <span className="text-xl font-bold text-[#1C1E21] hidden sm:block">معلم</span>}
    </Link>
  );
}
