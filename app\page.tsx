import Link from "next/link"
import Image from "next/image"
import { Star, Users, CheckCircle, ArrowLeft } from "lucide-react"
import { categories } from "@/lib/data"
import { Footer } from "@/components/footer"

export default function HomePage() {
  const featuredCategories = categories.slice(0, 6)

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-[#1877F2] to-[#166FE5] text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                مرحباً بك في
                <span className="block text-yellow-300">M3allam</span>
              </h1>
              <p className="text-xl mb-8 text-blue-100">
                منصتك الموثوقة للحصول على أفضل الخدمات المنزلية من مقدمي خدمات محترفين ومعتمدين
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/categories" className="btn-primary bg-white text-[#1877F2] hover:bg-gray-100 text-center">
                  احجز خدمة الآن
                </Link>
                <Link
                  href="/auth"
                  className="btn-secondary bg-transparent border-2 border-white text-white hover:bg-white hover:text-[#1877F2] text-center"
                >
                  انضم كمقدم خدمة
                </Link>
              </div>
            </div>
            <div className="hidden md:block">
              <Image
                src="/placeholder.svg?height=400&width=500"
                alt="خدمات منزلية"
                width={500}
                height={400}
                className="rounded-lg shadow-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div className="p-6">
              <div className="w-16 h-16 bg-[#1877F2] rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-3xl font-bold text-[#1C1E21] mb-2">500+</h3>
              <p className="text-gray-600">مقدم خدمة معتمد</p>
            </div>
            <div className="p-6">
              <div className="w-16 h-16 bg-[#1877F2] rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-3xl font-bold text-[#1C1E21] mb-2">2000+</h3>
              <p className="text-gray-600">خدمة مكتملة</p>
            </div>
            <div className="p-6">
              <div className="w-16 h-16 bg-[#1877F2] rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-3xl font-bold text-[#1C1E21] mb-2">4.8</h3>
              <p className="text-gray-600">متوسط التقييم</p>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-16 bg-[#F0F2F5]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-[#1C1E21] mb-4">لماذا تختار M3allam؟</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              نحن نربط بينك وبين أفضل مقدمي الخدمات المنزلية في منطقتك
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="card text-center">
              <div className="text-4xl mb-4">🔍</div>
              <h3 className="text-xl font-bold mb-3">سهولة البحث</h3>
              <p className="text-gray-600">ابحث عن الخدمة التي تحتاجها بسهولة وسرعة</p>
            </div>
            <div className="card text-center">
              <div className="text-4xl mb-4">✅</div>
              <h3 className="text-xl font-bold mb-3">مقدمو خدمات معتمدون</h3>
              <p className="text-gray-600">جميع مقدمي الخدمات لدينا معتمدون ومراجعون</p>
            </div>
            <div className="card text-center">
              <div className="text-4xl mb-4">💰</div>
              <h3 className="text-xl font-bold mb-3">أسعار شفافة</h3>
              <p className="text-gray-600">أسعار واضحة ومحددة مسبقاً بدون مفاجآت</p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Categories */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-[#1C1E21] mb-4">الخدمات المتاحة</h2>
            <p className="text-xl text-gray-600">اختر من بين مجموعة واسعة من الخدمات المنزلية</p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {featuredCategories.map((category) => (
              <Link
                key={category.id}
                href={`/categories/${category.id}`}
                className="card hover:shadow-lg transition-shadow text-center group"
              >
                <div className="text-4xl mb-3 group-hover:scale-110 transition-transform">{category.icon}</div>
                <h3 className="font-bold text-[#1C1E21] mb-2">{category.name}</h3>
                <p className="text-sm text-gray-600">{category.description}</p>
              </Link>
            ))}
          </div>
          <div className="text-center mt-8">
            <Link href="/categories" className="btn-primary inline-flex items-center gap-2">
              عرض جميع الفئات
              <ArrowLeft className="w-4 h-4" />
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-[#1877F2] text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">جاهز لحجز خدمتك القادمة؟</h2>
          <p className="text-xl mb-8 text-blue-100">احصل على خدمة احترافية في منزلك خلال دقائق</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/categories" className="btn-primary bg-white text-[#1877F2] hover:bg-gray-100">
              احجز خدمة الآن
            </Link>
            <Link
              href="/auth"
              className="btn-secondary bg-transparent border-2 border-white text-white hover:bg-white hover:text-[#1877F2]"
            >
              انضم كمقدم خدمة
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  )
}
