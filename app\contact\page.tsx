import Link from "next/link"
import { ArrowRight, Mail, Phone, MapPin, Send } from "lucide-react"
import { Footer } from "@/components/footer"

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-[#F0F2F5]">
      {/* Hero Section */}
      <section className="bg-[#1877F2] text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">اتصل بنا</h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">نحن هنا للإجابة على استفساراتك ومساعدتك</p>
          </div>
        </div>
      </section>

      {/* Breadcrumb */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-2 text-sm">
            <Link href="/" className="text-[#1877F2] hover:underline">
              الرئيسية
            </Link>
            <ArrowRight className="w-4 h-4 text-gray-400" />
            <span className="text-gray-600">اتصل بنا</span>
          </div>
        </div>
      </div>

      {/* Contact Info & Form */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Contact Info */}
            <div className="lg:col-span-1">
              <div className="card mb-6">
                <h2 className="text-2xl font-bold text-[#1C1E21] mb-6">معلومات الاتصال</h2>
                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <Mail className="w-5 h-5 text-[#1877F2]" />
                    </div>
                    <div>
                      <h3 className="font-bold text-[#1C1E21] mb-1">البريد الإلكتروني</h3>
                      <p className="text-gray-600"><EMAIL></p>
                      <p className="text-gray-600"><EMAIL></p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <Phone className="w-5 h-5 text-[#1877F2]" />
                    </div>
                    <div>
                      <h3 className="font-bold text-[#1C1E21] mb-1">الهاتف</h3>
                      <p className="text-gray-600">920000000</p>
                      <p className="text-gray-600">+966 50 000 0000</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <MapPin className="w-5 h-5 text-[#1877F2]" />
                    </div>
                    <div>
                      <h3 className="font-bold text-[#1C1E21] mb-1">العنوان</h3>
                      <p className="text-gray-600">شارع الملك فهد، حي العليا</p>
                      <p className="text-gray-600">الرياض، المملكة العربية السعودية</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="card">
                <h3 className="text-lg font-bold text-[#1C1E21] mb-4">ساعات العمل</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">الأحد - الخميس</span>
                    <span className="font-medium">9:00 ص - 6:00 م</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">الجمعة</span>
                    <span className="font-medium">مغلق</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">السبت</span>
                    <span className="font-medium">10:00 ص - 2:00 م</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Contact Form */}
            <div className="lg:col-span-2">
              <div className="card">
                <h2 className="text-2xl font-bold text-[#1C1E21] mb-6">أرسل لنا رسالة</h2>
                <form className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل</label>
                      <input
                        type="text"
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                        placeholder="أدخل اسمك الكامل"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                      <input
                        type="email"
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                        placeholder="أدخل بريدك الإلكتروني"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">الموضوع</label>
                    <input
                      type="text"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                      placeholder="موضوع الرسالة"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">الرسالة</label>
                    <textarea
                      rows={6}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                      placeholder="اكتب رسالتك هنا..."
                    ></textarea>
                  </div>
                  <button type="submit" className="btn-primary flex items-center justify-center gap-2">
                    <Send className="w-4 h-4" />
                    إرسال الرسالة
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Map */}
      <section className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="card p-0 overflow-hidden">
            <div className="bg-gray-200 h-96 w-full flex items-center justify-center">
              <div className="text-center">
                <MapPin className="w-12 h-12 text-[#1877F2] mx-auto mb-4" />
                <p className="text-lg font-medium">خريطة الموقع</p>
                <p className="text-gray-600">الرياض، المملكة العربية السعودية</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-[#1C1E21] mb-4">الأسئلة الشائعة</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">إجابات على الأسئلة الأكثر شيوعاً</p>
          </div>
          <div className="max-w-3xl mx-auto space-y-6">
            <div className="card">
              <h3 className="text-lg font-bold text-[#1C1E21] mb-2">كيف يمكنني حجز خدمة؟</h3>
              <p className="text-gray-600">
                يمكنك حجز خدمة بسهولة من خلال اختيار الفئة المناسبة، ثم اختيار الخدمة المطلوبة، واختيار مقدم الخدمة
                المناسب، وتحديد الوقت والتاريخ المناسبين.
              </p>
            </div>
            <div className="card">
              <h3 className="text-lg font-bold text-[#1C1E21] mb-2">كيف يمكنني الانضمام كمقدم خدمة؟</h3>
              <p className="text-gray-600">
                للانضمام كمقدم خدمة، يرجى التسجيل في المنصة واختيار "مقدم خدمة" عند إنشاء الحساب، ثم إكمال ملفك الشخصي
                وتحديد الخدمات التي تقدمها.
              </p>
            </div>
            <div className="card">
              <h3 className="text-lg font-bold text-[#1C1E21] mb-2">هل يمكنني إلغاء حجز؟</h3>
              <p className="text-gray-600">
                نعم، يمكنك إلغاء الحجز قبل 24 ساعة من الموعد المحدد دون أي رسوم. الإلغاء بعد ذلك قد يخضع لرسوم إلغاء.
              </p>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
