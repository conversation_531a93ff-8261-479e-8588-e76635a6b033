import type React from "react";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Navigation } from "@/components/navigation";
import { QueryProvider } from "@/lib/providers/query-provider";
import { AuthProvider } from "@/lib/auth/auth-context";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "M3allam - منصة الخدمات المنزلية",
  description: "منصة تربط بين العملاء ومقدمي الخدمات المنزلية",
  generator: "v0.dev",
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="ar" dir="rtl">
      <body className={`${inter.className} bg-gray-50`}>
        <AuthProvider>
          <QueryProvider>
            <Navigation />
            <main className="min-h-screen">{children}</main>
          </QueryProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
