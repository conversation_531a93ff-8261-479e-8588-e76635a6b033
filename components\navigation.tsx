"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { useState } from "react"
import { Home, Grid3X3, User, LogIn, Menu, X } from "lucide-react"

export function Navigation() {
  const pathname = usePathname()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const navItems = [
    { href: "/", label: "الرئيسية", icon: Home },
    { href: "/categories", label: "الفئات", icon: Grid3X3 },
    { href: "/dashboard", label: "حسابي", icon: User },
    { href: "/auth", label: "تسجيل الدخول", icon: LogIn },
  ]

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen)
  }

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false)
  }

  return (
    <>
      <nav className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2 space-x-reverse" onClick={closeMobileMenu}>
              <div className="w-8 h-8 bg-[#1877F2] rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">م</span>
              </div>
              <span className="text-xl font-bold text-[#1C1E21]">M3allam</span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8 space-x-reverse">
              {navItems.map((item) => {
                const Icon = item.icon
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={`flex items-center space-x-2 space-x-reverse px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      pathname === item.href
                        ? "text-[#1877F2] bg-blue-50"
                        : "text-gray-700 hover:text-[#1877F2] hover:bg-gray-50"
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{item.label}</span>
                  </Link>
                )
              })}
            </div>

            {/* Mobile Menu Button */}
            <div className="md:hidden">
              <button
                onClick={toggleMobileMenu}
                className="text-gray-700 hover:text-[#1877F2] p-2 rounded-md transition-colors"
                aria-label="فتح القائمة"
              >
                {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div className="fixed inset-0 z-40 md:hidden">
          {/* Backdrop */}
          <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onClick={closeMobileMenu} />

          {/* Menu Panel */}
          <div className="fixed top-16 right-0 left-0 bg-white border-t border-gray-200 shadow-lg">
            <div className="px-4 py-2 space-y-1">
              {navItems.map((item) => {
                const Icon = item.icon
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    onClick={closeMobileMenu}
                    className={`flex items-center space-x-3 space-x-reverse px-4 py-3 rounded-lg text-base font-medium transition-colors ${
                      pathname === item.href
                        ? "text-[#1877F2] bg-blue-50"
                        : "text-gray-700 hover:text-[#1877F2] hover:bg-gray-50"
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{item.label}</span>
                  </Link>
                )
              })}
            </div>

            {/* Mobile CTA Section */}
            <div className="px-4 py-4 border-t border-gray-200 bg-gray-50">
              <div className="space-y-2">
                <Link
                  href="/categories"
                  onClick={closeMobileMenu}
                  className="block w-full text-center bg-[#1877F2] hover:bg-[#166FE5] text-white font-medium px-4 py-2 rounded-lg transition-colors"
                >
                  احجز خدمة الآن
                </Link>
                <Link
                  href="/auth"
                  onClick={closeMobileMenu}
                  className="block w-full text-center bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium px-4 py-2 rounded-lg transition-colors"
                >
                  انضم كمقدم خدمة
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
