"use client";

import { useState } from "react";
import {
  Calendar,
  Clock,
  Star,
  User,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  History,
  Plus,
  Edit,
  Trash2,
  Phone,
  Mail,
  MapPin,
  Eye,
  ToggleLeft,
  ToggleRight,
  MessageSquare,
} from "lucide-react";
import { Footer } from "@/components/footer";
import { ProtectedRoute } from "@/components/auth/protected-route";

// Mock data based on ProviderUserService entity
const mockProviderServices = [
  {
    id: "1",
    service: { id: 1, name: "تركيب الأسلاك", category: "الكهرباء" },
    bio: "خبرة 10 سنوات في تركيب الأسلاك الكهربائية للمنازل والمكاتب",
    rating: 4.8,
    reviewCount: 156,
    isAvailable: true,
    availability: "ANYTIME",
    contacts: [
      { id: 1, type: "phone", value: "**********", label: "هاتف أساسي" },
      { id: 2, type: "email", value: "<EMAIL>", label: "بريد إلكتروني" },
    ],
    createdAt: "2024-01-15",
    updatedAt: "2024-01-20",
  },
  {
    id: "2",
    service: { id: 2, name: "إصلاح الأعطال", category: "الكهرباء" },
    bio: "متخصص في تشخيص وإصلاح جميع أنواع الأعطال الكهربائية",
    rating: 4.6,
    reviewCount: 89,
    isAvailable: false,
    availability: "WEEKDAYS",
    contacts: [{ id: 3, type: "phone", value: "**********", label: "هاتف العمل" }],
    createdAt: "2024-01-10",
    updatedAt: "2024-01-18",
  },
];

const availabilityOptions = [
  { value: "ANYTIME", label: "في أي وقت" },
  { value: "WEEKDAYS", label: "أيام العمل فقط" },
  { value: "WEEKENDS", label: "عطلة نهاية الأسبوع" },
  { value: "EVENINGS", label: "المساء فقط" },
];

export default function ProviderDashboardPage() {
  const [activeTab, setActiveTab] = useState("overview");
  const [showAddServiceModal, setShowAddServiceModal] = useState(false);
  const [editingService, setEditingService] = useState(null);

  const tabs = [
    { id: "overview", label: "نظرة عامة", icon: User },
    { id: "services", label: "خدماتي", icon: Settings },
    { id: "bookings", label: "الحجوزات", icon: Calendar },
    { id: "reviews", label: "التقييمات", icon: Star },
    { id: "profile", label: "الملف الشخصي", icon: Settings },
  ];

  return (
    <ProtectedRoute requiredRole="provider">
      <div className="min-h-screen bg-[#F0F2F5]">
        <div className="py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-[#1C1E21] mb-2">لوحة تحكم مقدم الخدمة</h1>
              <p className="text-gray-600">مرحباً بك، سعيد الكهربائي</p>
            </div>

            <div className="grid lg:grid-cols-4 gap-8">
              {/* Sidebar */}
              <div className="lg:col-span-1">
                <div className="card">
                  <div className="flex items-center gap-3 mb-6 pb-6 border-b border-gray-200">
                    <img src="/placeholder.svg?height=60&width=60" alt="صورة مقدم الخدمة" className="w-15 h-15 rounded-full object-cover" />
                    <div>
                      <h3 className="font-bold text-[#1C1E21]">سعيد الكهربائي</h3>
                      <p className="text-sm text-gray-600">مقدم خدمة</p>
                      <div className="flex items-center gap-1 mt-1">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="text-sm text-gray-600">4.7 (245 تقييم)</span>
                      </div>
                    </div>
                  </div>

                  <nav className="space-y-2">
                    {tabs.map((tab) => {
                      const Icon = tab.icon;
                      return (
                        <button
                          key={tab.id}
                          onClick={() => setActiveTab(tab.id)}
                          className={`w-full flex items-center gap-3 p-3 rounded-lg text-right transition-colors ${
                            activeTab === tab.id ? "bg-[#1877F2] text-white" : "text-gray-700 hover:bg-gray-100"
                          }`}
                        >
                          <Icon className="w-5 h-5" />
                          <span>{tab.label}</span>
                        </button>
                      );
                    })}
                  </nav>
                </div>
              </div>

              {/* Main Content */}
              <div className="lg:col-span-3">
                {activeTab === "overview" && (
                  <div className="space-y-6">
                    {/* Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                      <div className="card text-center">
                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                          <Settings className="w-6 h-6 text-[#1877F2]" />
                        </div>
                        <h3 className="text-2xl font-bold text-[#1C1E21] mb-1">{mockProviderServices.length}</h3>
                        <p className="text-gray-600">خدمات نشطة</p>
                      </div>
                      <div className="card text-center">
                        <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                          <Calendar className="w-6 h-6 text-green-600" />
                        </div>
                        <h3 className="text-2xl font-bold text-[#1C1E21] mb-1">24</h3>
                        <p className="text-gray-600">حجوزات هذا الشهر</p>
                      </div>
                      <div className="card text-center">
                        <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                          <Star className="w-6 h-6 text-yellow-600" />
                        </div>
                        <h3 className="text-2xl font-bold text-[#1C1E21] mb-1">4.7</h3>
                        <p className="text-gray-600">متوسط التقييم</p>
                      </div>
                      <div className="card text-center">
                        <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                          <MessageSquare className="w-6 h-6 text-purple-600" />
                        </div>
                        <h3 className="text-2xl font-bold text-[#1C1E21] mb-1">245</h3>
                        <p className="text-gray-600">إجمالي التقييمات</p>
                      </div>
                    </div>

                    {/* Quick Actions */}
                    <div className="card">
                      <h3 className="text-lg font-bold text-[#1C1E21] mb-4">إجراءات سريعة</h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <button
                          onClick={() => setActiveTab("services")}
                          className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-[#1877F2] hover:bg-blue-50 transition-colors text-center"
                        >
                          <Plus className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                          <p className="text-sm font-medium text-gray-600">إضافة خدمة جديدة</p>
                        </button>
                        <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center">
                          <Calendar className="w-8 h-8 text-[#1877F2] mx-auto mb-2" />
                          <p className="text-sm font-medium text-gray-700">عرض الحجوزات</p>
                        </button>
                        <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center">
                          <Star className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
                          <p className="text-sm font-medium text-gray-700">مراجعة التقييمات</p>
                        </button>
                      </div>
                    </div>

                    {/* Recent Activity */}
                    <div className="card">
                      <h3 className="text-lg font-bold text-[#1C1E21] mb-4">النشاط الأخير</h3>
                      <div className="space-y-4">
                        <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                          <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                          <div>
                            <p className="font-medium text-[#1C1E21]">حجز جديد لخدمة تركيب الأسلاك</p>
                            <p className="text-sm text-gray-600">العميل: أحمد محمد - التاريخ: 25 يناير 2024</p>
                            <p className="text-xs text-gray-500 mt-1">منذ ساعتين</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                          <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                          <div>
                            <p className="font-medium text-[#1C1E21]">تقييم جديد (5 نجوم)</p>
                            <p className="text-sm text-gray-600">"خدمة ممتازة وسريعة، أنصح بالتعامل معه"</p>
                            <p className="text-xs text-gray-500 mt-1">أمس</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === "services" && (
                  <div className="space-y-6">
                    {/* Services Header */}
                    <div className="flex items-center justify-between">
                      <h2 className="text-2xl font-bold text-[#1C1E21]">إدارة الخدمات</h2>
                      <button onClick={() => setShowAddServiceModal(true)} className="btn-primary flex items-center gap-2">
                        <Plus className="w-4 h-4" />
                        إضافة خدمة جديدة
                      </button>
                    </div>

                    {/* Services List */}
                    <div className="space-y-4">
                      {mockProviderServices.map((providerService) => (
                        <div key={providerService.id} className="card">
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex-1">
                              <div className="flex items-center gap-3 mb-2">
                                <h3 className="text-xl font-bold text-[#1C1E21]">{providerService.service.name}</h3>
                                <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">{providerService.service.category}</span>
                                <button
                                  onClick={() => {
                                    // Toggle availability
                                    console.log("Toggle availability for service:", providerService.id);
                                  }}
                                  className="flex items-center gap-1"
                                >
                                  {providerService.isAvailable ? (
                                    <ToggleRight className="w-6 h-6 text-green-500" />
                                  ) : (
                                    <ToggleLeft className="w-6 h-6 text-gray-400" />
                                  )}
                                  <span className={`text-sm ${providerService.isAvailable ? "text-green-600" : "text-gray-500"}`}>
                                    {providerService.isAvailable ? "متاح" : "غير متاح"}
                                  </span>
                                </button>
                              </div>
                              <p className="text-gray-600 mb-3">{providerService.bio}</p>

                              {/* Service Stats */}
                              <div className="flex items-center gap-6 text-sm text-gray-600 mb-4">
                                <div className="flex items-center gap-1">
                                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                                  <span>
                                    {providerService.rating} ({providerService.reviewCount} تقييم)
                                  </span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Clock className="w-4 h-4" />
                                  <span>{availabilityOptions.find((opt) => opt.value === providerService.availability)?.label}</span>
                                </div>
                              </div>

                              {/* Contacts */}
                              <div className="mb-4">
                                <h4 className="font-medium text-[#1C1E21] mb-2">طرق التواصل:</h4>
                                <div className="flex flex-wrap gap-2">
                                  {providerService.contacts.map((contact) => (
                                    <div key={contact.id} className="flex items-center gap-2 px-3 py-1 bg-gray-100 rounded-full text-sm">
                                      {contact.type === "phone" ? (
                                        <Phone className="w-3 h-3" />
                                      ) : contact.type === "email" ? (
                                        <Mail className="w-3 h-3" />
                                      ) : (
                                        <MapPin className="w-3 h-3" />
                                      )}
                                      <span>{contact.value}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="flex items-center gap-2">
                              <button
                                onClick={() => setEditingService(providerService as any)}
                                className="p-2 text-gray-600 hover:text-[#1877F2] hover:bg-blue-50 rounded-lg transition-colors"
                                title="تعديل"
                              >
                                <Edit className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => console.log("View details:", providerService.id)}
                                className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                                title="عرض التفاصيل"
                              >
                                <Eye className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => console.log("Delete service:", providerService.id)}
                                className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                                title="حذف"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>

                          {/* Service Footer */}
                          <div className="flex items-center justify-between pt-4 border-t border-gray-200 text-sm text-gray-500">
                            <span>تم الإنشاء: {providerService.createdAt}</span>
                            <span>آخر تحديث: {providerService.updatedAt}</span>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Empty State */}
                    {mockProviderServices.length === 0 && (
                      <div className="card text-center py-12">
                        <Settings className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                        <h3 className="text-xl font-bold text-[#1C1E21] mb-2">لا توجد خدمات مضافة</h3>
                        <p className="text-gray-600 mb-6">ابدأ بإضافة خدماتك لتتمكن من استقبال الحجوزات</p>
                        <button onClick={() => setShowAddServiceModal(true)} className="btn-primary">
                          إضافة خدمة جديدة
                        </button>
                      </div>
                    )}
                  </div>
                )}

                {activeTab === "bookings" && (
                  <div className="card">
                    <h3 className="text-lg font-bold text-[#1C1E21] mb-4">الحجوزات الواردة</h3>
                    <div className="space-y-4">
                      {/* Sample bookings */}
                      <div className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-bold text-[#1C1E21]">تركيب أسلاك كهربائية</h4>
                          <span className="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded-full">في الانتظار</span>
                        </div>
                        <div className="grid md:grid-cols-3 gap-4 text-sm mb-4">
                          <div>
                            <p className="text-gray-600">العميل</p>
                            <p className="font-medium">أحمد محمد</p>
                          </div>
                          <div>
                            <p className="text-gray-600">التاريخ المطلوب</p>
                            <p className="font-medium">25 يناير 2024</p>
                          </div>
                          <div>
                            <p className="text-gray-600">الوقت</p>
                            <p className="font-medium">10:00 صباحاً</p>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <button className="btn-primary text-sm">قبول</button>
                          <button className="btn-secondary text-sm">رفض</button>
                          <button className="text-[#1877F2] text-sm hover:underline">تفاصيل أكثر</button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === "reviews" && (
                  <div className="card">
                    <h3 className="text-lg font-bold text-[#1C1E21] mb-4">التقييمات والمراجعات</h3>
                    <div className="space-y-4">
                      {/* Sample reviews */}
                      <div className="border-b border-gray-200 pb-4">
                        <div className="flex items-start gap-3">
                          <img src="/placeholder.svg?height=40&width=40" alt="صورة العميل" className="w-10 h-10 rounded-full object-cover" />
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h4 className="font-medium text-[#1C1E21]">أحمد محمد</h4>
                              <div className="flex items-center">
                                {[1, 2, 3, 4, 5].map((star) => (
                                  <Star key={star} className="w-4 h-4 text-yellow-400 fill-current" />
                                ))}
                              </div>
                            </div>
                            <p className="text-gray-600 text-sm mb-2">
                              "خدمة ممتازة وسريعة، تم إنجاز العمل في الوقت المحدد وبجودة عالية. أنصح بالتعامل معه."
                            </p>
                            <p className="text-xs text-gray-500">منذ يومين</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === "profile" && (
                  <div className="space-y-6">
                    <div className="card">
                      <h3 className="text-lg font-bold text-[#1C1E21] mb-4">المعلومات الشخصية</h3>
                      <form className="space-y-4">
                        <div className="grid md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">الاسم</label>
                            <input
                              type="text"
                              defaultValue="سعيد الكهربائي"
                              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                            <input
                              type="tel"
                              defaultValue="**********"
                              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                            />
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                          <input
                            type="email"
                            defaultValue="<EMAIL>"
                            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">نبذة مهنية</label>
                          <textarea
                            rows={4}
                            defaultValue="كهربائي محترف مع خبرة 15 سنة في جميع أنواع الأعمال الكهربائية للمنازل والمكاتب. متخصص في التركيبات الحديثة وأنظمة الأمان."
                            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                          />
                        </div>
                        <button type="submit" className="btn-primary">
                          حفظ التغييرات
                        </button>
                      </form>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </ProtectedRoute>
  );
}
