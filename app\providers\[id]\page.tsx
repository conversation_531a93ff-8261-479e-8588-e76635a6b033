import Link from "next/link"
import { ArrowRight, Star, MapPin, Phone, Clock, CheckCircle } from "lucide-react"
import { providers, reviews } from "@/lib/data"
import { Footer } from "@/components/footer"

export default function ProviderProfilePage({ params }: { params: { id: string } }) {
  const providerId = Number.parseInt(params.id)
  const provider = providers.find((p) => p.id === providerId)
  const providerReviews = reviews.filter((r) => r.providerId === providerId)

  if (!provider) {
    return <div>مقدم الخدمة غير موجود</div>
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < Math.floor(rating) ? "text-yellow-400 fill-current" : "text-gray-300"}`}
      />
    ))
  }

  return (
    <div className="min-h-screen bg-[#F0F2F5]">
      <div className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Breadcrumb */}
          <div className="flex items-center gap-2 mb-6 text-sm">
            <Link href="/categories" className="text-[#1877F2] hover:underline">
              الفئات
            </Link>
            <ArrowRight className="w-4 h-4 text-gray-400" />
            <span className="text-gray-600">ملف مقدم الخدمة</span>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Provider Info */}
            <div className="lg:col-span-2">
              <div className="card mb-6">
                <div className="flex items-start gap-6 mb-6">
                  <img
                    src={provider.image || "/placeholder.svg"}
                    alt={provider.name}
                    className="w-24 h-24 rounded-full object-cover"
                  />
                  <div className="flex-1">
                    <h1 className="text-3xl font-bold text-[#1C1E21] mb-2">{provider.name}</h1>
                    <div className="flex items-center gap-2 mb-3">
                      <div className="flex items-center gap-1">{renderStars(provider.rating)}</div>
                      <span className="font-bold text-[#1C1E21]">{provider.rating}</span>
                      <span className="text-gray-600">({providerReviews.length} تقييم)</span>
                    </div>
                    <p className="text-gray-600 mb-4">{provider.bio}</p>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span>{provider.completedJobs} خدمة مكتملة</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <MapPin className="w-4 h-4" />
                        <span>{provider.location}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4 mb-6">
                  <div className="flex items-center gap-2 text-gray-600">
                    <Phone className="w-4 h-4" />
                    <span>{provider.phone}</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-600">
                    <Clock className="w-4 h-4" />
                    <span>{provider.availability}</span>
                  </div>
                </div>

                <div className="mb-6">
                  <h3 className="text-lg font-bold text-[#1C1E21] mb-3">الخدمات المقدمة</h3>
                  <div className="flex flex-wrap gap-2">
                    {provider.services.map((service, index) => (
                      <span
                        key={index}
                        className="bg-blue-100 text-[#1877F2] px-3 py-1 rounded-full text-sm font-medium"
                      >
                        {service}
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              {/* Reviews */}
              <div className="card">
                <h3 className="text-lg font-bold text-[#1C1E21] mb-4">آراء العملاء</h3>
                <div className="space-y-4">
                  {providerReviews.map((review) => (
                    <div key={review.id} className="border-b border-gray-200 pb-4 last:border-b-0">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-[#1C1E21]">{review.author}</span>
                          <div className="flex items-center gap-1">{renderStars(review.rating)}</div>
                        </div>
                        <span className="text-sm text-gray-500">{review.date}</span>
                      </div>
                      <p className="text-gray-600">{review.content}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Booking Card */}
            <div className="lg:col-span-1">
              <div className="card sticky top-24">
                <h3 className="text-lg font-bold text-[#1C1E21] mb-4">احجز خدمة</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">اختر الخدمة</label>
                    <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent">
                      {provider.services.map((service, index) => (
                        <option key={index} value={service}>
                          {service}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ الخدمة</label>
                    <input
                      type="date"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">وقت الخدمة</label>
                    <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent">
                      <option>8:00 صباحاً</option>
                      <option>10:00 صباحاً</option>
                      <option>12:00 ظهراً</option>
                      <option>2:00 مساءً</option>
                      <option>4:00 مساءً</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">ملاحظات إضافية</label>
                    <textarea
                      rows={3}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#1877F2] focus:border-transparent"
                      placeholder="اكتب أي ملاحظات أو تفاصيل إضافية..."
                    ></textarea>
                  </div>
                  <button className="w-full btn-primary">احجز الآن</button>
                  <p className="text-xs text-gray-500 text-center">سيتم تأكيد الحجز خلال 24 ساعة</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  )
}
