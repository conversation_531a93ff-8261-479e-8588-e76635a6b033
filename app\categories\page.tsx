import Link from "next/link"
import Image from "next/image"
import { categories } from "@/lib/data"
import { ArrowLeft } from "lucide-react"
import { Footer } from "@/components/footer"

export default function CategoriesPage() {
  return (
    <div className="min-h-screen bg-[#F0F2F5]">
      <div className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h1 className="text-3xl md:text-4xl font-bold text-[#1C1E21] mb-4">جميع فئات الخدمات</h1>
            <p className="text-xl text-gray-600">اختر الفئة المناسبة لاحتياجاتك واعثر على أفضل مقدمي الخدمات</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {categories.map((category) => (
              <Link
                key={category.id}
                href={`/categories/${category.id}`}
                className="card hover:shadow-lg transition-all duration-300 group"
              >
                <div className="relative mb-4">
                  <Image
                    src={category.image || "/placeholder.svg"}
                    alt={category.name}
                    width={300}
                    height={200}
                    className="w-full h-48 object-cover rounded-lg"
                  />
                  <div className="absolute top-4 right-4 text-3xl bg-white rounded-full w-12 h-12 flex items-center justify-center shadow-md">
                    {category.icon}
                  </div>
                </div>

                <h3 className="text-xl font-bold text-[#1C1E21] mb-2 group-hover:text-[#1877F2] transition-colors">
                  {category.name}
                </h3>
                <p className="text-gray-600 mb-4">{category.description}</p>

                <div className="flex items-center justify-between">
                  <span className="text-[#1877F2] font-medium">عرض الخدمات</span>
                  <ArrowLeft className="w-4 h-4 text-[#1877F2] group-hover:translate-x-1 transition-transform" />
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
      <Footer />
    </div>
  )
}
