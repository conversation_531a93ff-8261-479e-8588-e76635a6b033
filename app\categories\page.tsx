"use client";

import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON>ef<PERSON>, Loader2 } from "lucide-react";
import { Footer } from "@/components/footer";
import { useCategories } from "@/lib/hooks/useCategories";

export default function CategoriesPage() {
  const { data: categories, isLoading, error } = useCategories();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#F0F2F5]">
        <div className="py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="mb-8">
              <h1 className="text-3xl md:text-4xl font-bold text-[#1C1E21] mb-4">جميع فئات الخدمات</h1>
              <p className="text-xl text-gray-600">اختر الفئة المناسبة لاحتياجاتك واعثر على أفضل مقدمي الخدمات</p>
            </div>
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-[#1877F2]" />
              <span className="mr-2 text-lg text-gray-600">جاري تحميل الفئات...</span>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[#F0F2F5]">
        <div className="py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="mb-8">
              <h1 className="text-3xl md:text-4xl font-bold text-[#1C1E21] mb-4">جميع فئات الخدمات</h1>
              <p className="text-xl text-gray-600">اختر الفئة المناسبة لاحتياجاتك واعثر على أفضل مقدمي الخدمات</p>
            </div>
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <p className="text-lg text-red-600 mb-4">حدث خطأ في تحميل الفئات</p>
                <button
                  onClick={() => window.location.reload()}
                  className="bg-[#1877F2] text-white px-6 py-2 rounded-lg hover:bg-[#166FE5] transition-colors"
                >
                  إعادة المحاولة
                </button>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#F0F2F5]">
      <div className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h1 className="text-3xl md:text-4xl font-bold text-[#1C1E21] mb-4">جميع فئات الخدمات</h1>
            <p className="text-xl text-gray-600">اختر الفئة المناسبة لاحتياجاتك واعثر على أفضل مقدمي الخدمات</p>
          </div>

          {categories && categories.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {categories.map((category) => (
                <Link key={category.id} href={`/categories/${category.id}`} className="card hover:shadow-lg transition-all duration-300 group">
                  <div className="relative mb-4">
                    <Image
                      src={category.image || "/placeholder.svg"}
                      alt={category.name}
                      width={300}
                      height={200}
                      className="w-full h-48 object-cover rounded-lg"
                    />
                    <div className="absolute top-4 right-4 text-3xl bg-white rounded-full w-12 h-12 flex items-center justify-center shadow-md">
                      {category.icon}
                    </div>
                  </div>

                  <h3 className="text-xl font-bold text-[#1C1E21] mb-2 group-hover:text-[#1877F2] transition-colors">{category.name}</h3>
                  <p className="text-gray-600 mb-4">{category.description}</p>

                  <div className="flex items-center justify-between">
                    <span className="text-[#1877F2] font-medium">عرض الخدمات</span>
                    <ArrowLeft className="w-4 h-4 text-[#1877F2] group-hover:translate-x-1 transition-transform" />
                  </div>
                </Link>
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <p className="text-lg text-gray-600">لا توجد فئات متاحة حالياً</p>
              </div>
            </div>
          )}
        </div>
      </div>
      <Footer />
    </div>
  );
}
